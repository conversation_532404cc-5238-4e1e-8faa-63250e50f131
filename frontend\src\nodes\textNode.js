// textNode.js

import { useState, useEffect, useRef } from 'react';
import { BaseNode, HANDLE_CONFIGS, createHandle } from './BaseNode';
import { Position } from 'reactflow';

export const TextNode = ({ id, data }) => {
  const [currText, setCurrText] = useState(data?.text || '{{input}}');
  const [nodeSize, setNodeSize] = useState({ width: 220, height: 120 });
  const [variables, setVariables] = useState([]);
  const textareaRef = useRef(null);

  // Extract variables from text using regex for {{variableName}}
  const extractVariables = (text) => {
    const regex = /\{\{([a-zA-Z_$][a-zA-Z0-9_$]*)\}\}/g;
    const matches = [];
    let match;

    while ((match = regex.exec(text)) !== null) {
      const variableName = match[1];
      if (!matches.includes(variableName)) {
        matches.push(variableName);
      }
    }

    return matches;
  };

  // Calculate dynamic size based on text content
  const calculateSize = (text) => {
    const lines = text.split('\n');
    const maxLineLength = Math.max(...lines.map(line => line.length));

    // Smaller base dimensions for better canvas usage
    const baseWidth = 140;
    const baseHeight = 80;

    // Dynamic width based on content
    const charWidth = 6; // smaller character width for compact nodes
    const dynamicWidth = Math.max(baseWidth, Math.min(280, maxLineLength * charWidth + 40));

    // Dynamic height based on lines
    const lineHeight = 18;
    const dynamicHeight = Math.max(baseHeight, lines.length * lineHeight + 70);

    return { width: dynamicWidth, height: dynamicHeight };
  };

  const handleTextChange = (e) => {
    const newText = e.target.value;
    setCurrText(newText);

    // Update node size based on content
    const newSize = calculateSize(newText);
    setNodeSize(newSize);

    // Extract and update variables
    const newVariables = extractVariables(newText);
    setVariables(newVariables);
  };

  // Initialize variables and size on mount
  useEffect(() => {
    const initialVariables = extractVariables(currText);
    setVariables(initialVariables);

    const initialSize = calculateSize(currText);
    setNodeSize(initialSize);
  }, [currText]);

  // Create handles: one output handle + input handles for each variable
  const handles = [
    HANDLE_CONFIGS.sourceRight(`${id}-output`),
    ...variables.map((variable, index) =>
      createHandle(
        `${id}-${variable}`,
        'target',
        Position.Left,
        { top: `${30 + (index * 25)}%` }
      )
    )
  ];

  return (
    <BaseNode
      id={id}
      data={data}
      title="Text"
      handles={handles}
      nodeType="text"
      width={nodeSize.width}
      height={nodeSize.height}
    >
      <div style={{ height: '100%' }}>
        <label>
          Text:
          <textarea
            ref={textareaRef}
            value={currText}
            onChange={handleTextChange}
            placeholder="Enter text with variables like {{variableName}}"
            style={{
              fontSize: '11px',
              width: '100%',
              height: `${nodeSize.height - 80}px`,
              resize: 'none',
              border: '1px solid rgba(138, 43, 226, 0.3)',
              borderRadius: '6px',
              padding: '8px',
              fontFamily: "'Studio Feixen Sans', 'Inter', sans-serif",
              outline: 'none',
              transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)',
              backgroundColor: 'rgba(26, 11, 46, 0.8)',
              color: '#ffffff',
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
            }}
            onFocus={(e) => {
              e.target.style.borderColor = 'rgba(138, 43, 226, 0.8)';
              e.target.style.boxShadow = '0 0 0 2px rgba(138, 43, 226, 0.2)';
              e.target.style.backgroundColor = 'rgba(26, 11, 46, 0.9)';
            }}
            onBlur={(e) => {
              e.target.style.borderColor = 'rgba(138, 43, 226, 0.3)';
              e.target.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
              e.target.style.backgroundColor = 'rgba(26, 11, 46, 0.8)';
            }}
          />
        </label>

        {/* Display detected variables */}
        {variables.length > 0 && (
          <div style={{
            fontSize: '10px',
            color: '#ffffff',
            padding: '6px',
            backgroundColor: 'rgba(138, 43, 226, 0.2)',
            borderRadius: '6px',
            border: '1px solid rgba(138, 43, 226, 0.4)',
            fontWeight: '500'
          }}>
            <strong>Variables:</strong> {variables.join(', ')}
          </div>
        )}
      </div>
    </BaseNode>
  );
}
