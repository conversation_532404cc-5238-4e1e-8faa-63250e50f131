import { PipelineToolbar } from './toolbar';
import { PipelineUI } from './ui';
import { SubmitButton } from './submit';

// Debug logging for image/resource loading issues
console.log('App.js loaded successfully');

// Check if fonts are loading
document.fonts.ready.then(() => {
  console.log('Fonts loaded successfully');

  // Check specifically for Studio Feixen Sans
  const studioFeixenFont = new FontFace('Studio Feixen Sans', 'url(./fonts/Studio Feixen Fonts TRIAL/Studio Feixen Family TRIAL/1 Studio Feixen Sans Family TRIAL/2 TTF/StudioFeixenSansTRIAL-Regular.ttf)');

  studioFeixenFont.load().then(() => {
    console.log('✅ Studio Feixen Sans loaded successfully!');
    document.fonts.add(studioFeixenFont);
  }).catch((error) => {
    console.error('❌ Studio Feixen Sans loading failed:', error);
  });

}).catch((error) => {
  console.error('Font loading error:', error);
});

function App() {
  return (
    <div className="app-container">
      <PipelineToolbar />
      <PipelineUI />
      <SubmitButton />
    </div>
  );
}

export default App;
