/* Import VectorShift Fonts - with fallback */
@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=JetBrains+Mono:ital,wght@0,100..800;1,100..800&display=swap');

/* Fallback fonts in case Google Fonts fails */
@font-face {
  font-family: 'Studio Feixen Sans Fallback';
  src: local('Arial'), local('Helvetica'), local('sans-serif');
  font-weight: normal;
  font-style: normal;
}

/* Studio Feixen Sans Alternative - Using Inter as fallback with custom styling */
@font-face {
  font-family: 'Studio Feixen Sans';
  src: local('Inter'), local('Inter-Regular'), local('Arial'), local('Helvetica');
  font-weight: normal;
  font-style: normal;
}

/* CSS Reset and Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #1a0b2e 0%, #16213e 50%, #0f3460 100%);
  min-height: 100vh;
  color: #ffffff;
}

code {
  font-family: 'JetBrains Mono', source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* App Layout - Performance Optimized */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(135deg, #1a0b2e 0%, #16213e 50%, #0f3460 100%);
  color: #ffffff;
  font-family: 'Inter', sans-serif;
  position: relative;
}

/* Toolbar Styles - VectorShift Branding */
.pipeline-toolbar {
  background: rgba(26, 11, 46, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(138, 43, 226, 0.2);
  padding: 16px 24px;
  box-shadow: 0 4px 20px rgba(138, 43, 226, 0.1);
}

/* VectorShift Toolbar Header */
.toolbar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
}

.toolbar-brand {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.brand-logo {
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-svg {
  filter: drop-shadow(0 0 8px rgba(138, 43, 226, 0.5));
  transition: all 0.3s ease;
}

.logo-svg:hover {
  transform: scale(1.1) rotate(5deg);
  filter: drop-shadow(0 0 12px rgba(138, 43, 226, 0.8));
}

.brand-text {
  flex: 1;
}

.toolbar-title {
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
  background: linear-gradient(90deg, #ffffff 0%, #e2e8f0 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  white-space: nowrap;
}

.toolbar-nodes {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
  flex: 1;
  justify-content: flex-end;
}

/* Draggable Node Styles - VectorShift Professional */
.draggable-node {
  cursor: grab;
  min-width: 100px;
  max-width: 120px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background: linear-gradient(135deg, rgba(138, 43, 226, 0.15) 0%, rgba(79, 70, 229, 0.15) 100%);
  border: 1px solid rgba(138, 43, 226, 0.4);
  color: #ffffff;
  font-weight: 500;
  font-size: 10px;
  text-transform: uppercase;
  letter-spacing: 0.2px;
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  user-select: none;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(138, 43, 226, 0.1);
  flex-shrink: 0;
  padding: 0 8px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.draggable-node:hover {
  background: linear-gradient(135deg, rgba(138, 43, 226, 0.25) 0%, rgba(79, 70, 229, 0.25) 100%);
  border-color: rgba(138, 43, 226, 0.7);
  transform: translateY(-1px) scale(1.05);
  box-shadow: 0 4px 15px rgba(138, 43, 226, 0.3);
}

.draggable-node:active {
  cursor: grabbing;
  transform: translateY(0) scale(0.95);
}

/* Pipeline UI Container - VectorShift Style */
.pipeline-ui-container {
  flex: 1;
  position: relative;
  background: rgba(26, 11, 46, 0.3);
  margin: 16px;
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid rgba(138, 43, 226, 0.2);
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(138, 43, 226, 0.1);
  width: calc(100% - 32px);
  height: calc(100vh - 200px);
  min-height: 500px;
}

/* Submit Button Container - VectorShift Style */
.submit-container {
  padding: 16px 24px;
  background: rgba(26, 11, 46, 0.95);
  border-top: 1px solid rgba(138, 43, 226, 0.2);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  backdrop-filter: blur(20px);
  min-height: auto;
}

.submit-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.pipeline-stats {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-number {
  font-size: 20px;
  font-weight: 700;
  background: linear-gradient(90deg, #8b5cf6, #a855f7);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.stat-label {
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: rgba(255, 255, 255, 0.6);
}

.stat-divider {
  color: rgba(138, 43, 226, 0.5);
  font-size: 16px;
}

.getting-started {
  text-align: center;
}

.start-hint {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(138, 43, 226, 0.1);
  padding: 8px 16px;
  border-radius: 20px;
  border: 1px solid rgba(138, 43, 226, 0.2);
}

.submit-button {
  background: linear-gradient(135deg, #8b5cf6 0%, #6366f1 100%);
  color: #ffffff;
  border: none;
  padding: 10px 32px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(138, 43, 226, 0.3);
}

.submit-button:hover {
  background: linear-gradient(135deg, #a855f7 0%, #7c3aed 100%);
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(138, 43, 226, 0.4);
}

.submit-button:active {
  transform: translateY(0) scale(0.98);
}

.submit-button.disabled {
  background: linear-gradient(135deg, rgba(138, 43, 226, 0.3) 0%, rgba(99, 102, 241, 0.3) 100%);
  cursor: not-allowed;
  opacity: 0.6;
}

.submit-button.disabled:hover {
  transform: none;
  box-shadow: 0 4px 15px rgba(138, 43, 226, 0.2);
}

.submit-button:focus {
  outline: 2px solid #6366f1;
  outline-offset: 2px;
}

/* ReactFlow Custom Styling - VectorShift Theme */
.react-flow__controls {
  background: rgba(15, 12, 26, 0.95) !important;
  border: 2px solid rgba(138, 43, 226, 0.8) !important;
  border-radius: 16px !important;
  backdrop-filter: blur(20px) !important;
  box-shadow:
    0 8px 32px rgba(138, 43, 226, 0.6),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset !important;
  bottom: 20px !important;
  left: 20px !important;
  padding: 4px !important;
}

.react-flow__controls-button {
  background: rgba(138, 43, 226, 0.2) !important;
  border: 1px solid rgba(138, 43, 226, 0.5) !important;
  color: #ffffff !important;
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1) !important;
  border-radius: 8px !important;
  width: 28px !important;
  height: 28px !important;
  margin: 2px !important;
  font-size: 14px !important;
  font-weight: bold !important;
}

.react-flow__controls-button:hover {
  background: rgba(138, 43, 226, 0.4) !important;
  border-color: rgba(138, 43, 226, 1) !important;
  color: #ffffff !important;
  transform: scale(1.1) !important;
  box-shadow: 0 4px 16px rgba(138, 43, 226, 0.5) !important;
}

.react-flow__controls-button svg {
  fill: currentColor !important;
  width: 18px !important;
  height: 18px !important;
}

.react-flow__minimap {
  background: rgba(15, 12, 26, 0.95) !important;
  border: 2px solid rgba(138, 43, 226, 0.8) !important;
  border-radius: 16px !important;
  backdrop-filter: blur(20px) !important;
  box-shadow:
    0 8px 32px rgba(138, 43, 226, 0.6),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset !important;
  bottom: 20px !important;
  right: 20px !important;
}

.react-flow__edge-path {
  stroke: #8b5cf6 !important;
  stroke-width: 2px !important;
  filter: drop-shadow(0 0 4px rgba(139, 92, 246, 0.5)) !important;
}

.react-flow__edge.selected .react-flow__edge-path {
  stroke: #a855f7 !important;
  stroke-width: 3px !important;
  filter: drop-shadow(0 0 8px rgba(168, 85, 247, 0.8)) !important;
}

.react-flow__connection-line {
  stroke: #8b5cf6 !important;
  stroke-width: 2px !important;
  filter: drop-shadow(0 0 4px rgba(139, 92, 246, 0.5)) !important;
}

/* Custom scrollbar - VectorShift Style */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(26, 11, 46, 0.3);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #8b5cf6 0%, #6366f1 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #a855f7 0%, #7c3aed 100%);
}

/* VectorShift Professional Animations */
@keyframes vectorShiftGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(138, 43, 226, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(138, 43, 226, 0.5);
  }
}

@keyframes vectorShiftPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

@keyframes vectorShiftSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Apply animations to key elements */
.pipeline-toolbar {
  animation: vectorShiftSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.draggable-node {
  animation: vectorShiftSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.submit-button {
  animation: vectorShiftGlow 3s ease-in-out infinite;
}

/* Professional focus states */
.draggable-node:focus,
.submit-button:focus {
  outline: 2px solid rgba(138, 43, 226, 0.6);
  outline-offset: 2px;
}

/* Enhanced backdrop effects */
.app-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(138, 43, 226, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(79, 70, 229, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(168, 85, 247, 0.05) 0%, transparent 70%);
  pointer-events: none;
  z-index: -1;
  animation: vectorShiftBackdrop 20s ease-in-out infinite;
}

@keyframes vectorShiftBackdrop {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Professional node connection animations */
.react-flow__edge-path {
  animation: vectorShiftFlow 2s ease-in-out infinite;
}

@keyframes vectorShiftFlow {
  0%, 100% {
    stroke-dasharray: 5, 5;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dashoffset: 10;
  }
}

/* Toolbar section staggered animations */
.toolbar-section:nth-child(1) { animation-delay: 0.1s; }
.toolbar-section:nth-child(2) { animation-delay: 0.2s; }
.toolbar-section:nth-child(3) { animation-delay: 0.3s; }
.toolbar-section:nth-child(4) { animation-delay: 0.4s; }

/* Node hover glow effect */
.base-node {
  position: relative;
  overflow: visible;
}

.base-node::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, rgba(138, 43, 226, 0.3), rgba(79, 70, 229, 0.3));
  border-radius: 18px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.base-node:hover::before {
  opacity: 1;
}

/* Professional drag state */
.react-flow__node.dragging {
  transform: rotate(2deg) scale(1.05);
  filter: drop-shadow(0 10px 20px rgba(138, 43, 226, 0.4));
  z-index: 1000;
}

/* Submit button enhanced states */
.submit-button {
  position: relative;
  overflow: hidden;
}

.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.submit-button:hover::before {
  left: 100%;
}

/* VectorShift-style loading indicator */
@keyframes vectorShiftSpin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive Design for Mobile and Tablet */
@media (max-width: 768px) {
  .submit-container {
    padding: 12px 16px;
    gap: 8px;
  }

  .submit-button {
    padding: 8px 24px;
    font-size: 13px;
  }

  .pipeline-stats {
    gap: 8px;
    font-size: 12px;
  }

  .stat-number {
    font-size: 18px;
  }

  .stat-label {
    font-size: 11px;
  }

  .getting-started-text {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .submit-container {
    padding: 10px 12px;
    gap: 6px;
  }

  .submit-button {
    padding: 8px 20px;
    font-size: 12px;
  }

  .pipeline-stats {
    flex-direction: column;
    gap: 4px;
  }

  .stat-divider {
    display: none;
  }

  .stat-number {
    font-size: 16px;
  }

  .stat-label {
    font-size: 10px;
  }
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(138, 43, 226, 0.3);
  border-top: 2px solid #8b5cf6;
  border-radius: 50%;
  animation: vectorShiftSpin 1s linear infinite;
}

/* Professional focus indicators */
.react-flow__controls-button:focus {
  outline: 2px solid rgba(138, 43, 226, 0.6);
  outline-offset: 2px;
}

/* Enhanced minimap styling */
.react-flow__minimap-mask {
  fill: rgba(138, 43, 226, 0.1);
}

.react-flow__minimap-node {
  fill: rgba(138, 43, 226, 0.6);
  stroke: rgba(138, 43, 226, 0.8);
}

/* Node Input Field Styling - VectorShift Professional */
.node-input {
  min-width: 100px;
  max-width: 100%;
  white-space: nowrap;
  overflow-x: auto;
  padding: 6px 8px;
  border-radius: 6px;
  background-color: rgba(26, 11, 46, 0.8);
  color: #ffffff;
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
  font-size: 11px;
  font-weight: 500;
  border: 1px solid rgba(138, 43, 226, 0.5);
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  height: 32px;
  box-sizing: border-box;
}

.node-input:focus {
  border-color: rgba(138, 43, 226, 0.8);
  box-shadow: 0 0 0 2px rgba(138, 43, 226, 0.2);
  background-color: #1e1e2f;
}

.node-input:hover {
  border-color: rgba(138, 43, 226, 0.6);
  background-color: #1e1e2f;
}

.node-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* Input container with tooltip */
.input-container {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.input-tooltip {
  position: absolute;
  bottom: 100%;
  left: 0;
  background: rgba(26, 11, 46, 0.95);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  white-space: nowrap;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 200ms ease;
  border: 1px solid rgba(138, 43, 226, 0.4);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  margin-bottom: 4px;
}

.input-container:hover .input-tooltip {
  opacity: 1;
  visibility: visible;
}

/* Modal Styling - VectorShift Professional */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: modalBackdropFadeIn 0.3s ease;
}

@keyframes modalBackdropFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modal-container {
  background: #1e1e2f;
  border-radius: 12px;
  padding: 24px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  border: 1px solid rgba(138, 43, 226, 0.4);
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(138, 43, 226, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  animation: modalSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(138, 43, 226, 0.3);
}

.modal-title {
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
  font-size: 20px;
  font-weight: 600;
  color: #f5f5f5;
  display: flex;
  align-items: center;
  gap: 8px;
}

.modal-close {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 24px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 200ms ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.modal-close:hover {
  background: rgba(138, 43, 226, 0.2);
  color: #ffffff;
}

.modal-content {
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
  font-size: 16px;
  color: #f5f5f5;
  line-height: 1.6;
}

.modal-section {
  margin-bottom: 16px;
}

.modal-section:last-child {
  margin-bottom: 0;
}

.modal-stat {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(138, 43, 226, 0.1);
  border-radius: 8px;
  margin-bottom: 8px;
  border: 1px solid rgba(138, 43, 226, 0.2);
}

.modal-stat-icon {
  font-size: 18px;
}

.modal-stat-text {
  font-weight: 500;
}

.modal-status {
  padding: 12px 16px;
  border-radius: 8px;
  margin-top: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.modal-status.success {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
  color: #22c55e;
}

.modal-status.error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #ef4444;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid rgba(138, 43, 226, 0.3);
}

.modal-button {
  padding: 8px 16px;
  border-radius: 6px;
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 200ms ease;
  border: none;
}

.modal-button.primary {
  background: linear-gradient(135deg, #8b5cf6 0%, #6366f1 100%);
  color: white;
}

.modal-button.primary:hover {
  background: linear-gradient(135deg, #a855f7 0%, #7c3aed 100%);
  transform: translateY(-1px);
}

.modal-button.secondary {
  background: rgba(138, 43, 226, 0.1);
  color: #a855f7;
  border: 1px solid rgba(138, 43, 226, 0.3);
}

.modal-button.secondary:hover {
  background: rgba(138, 43, 226, 0.2);
}

/* Textarea styling to match node inputs */
textarea.node-textarea {
  min-width: 100%;
  padding: 6px 8px;
  border-radius: 6px;
  background-color: #1b1b2b;
  color: white;
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
  border: 1px solid rgba(138, 43, 226, 0.3);
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  resize: none;
}

textarea.node-textarea:focus {
  border-color: rgba(138, 43, 226, 0.8);
  box-shadow: 0 0 0 2px rgba(138, 43, 226, 0.2);
  background-color: #1e1e2f;
}

textarea.node-textarea:hover {
  border-color: rgba(138, 43, 226, 0.6);
  background-color: #1e1e2f;
}

textarea.node-textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* Node Label Styling for Better Visibility */
.base-node label {
  color: #ffffff;
  font-weight: 600;
  font-size: 11px;
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  margin-bottom: 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
  box-sizing: border-box;
  position: relative;
}

/* Standardize all node content containers */
.base-node > div {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

/* Fix inline label styling */
.base-node label.inline-label {
  flex-direction: row;
  align-items: center;
  gap: 6px;
  font-size: 11px;
}

.base-node label.inline-label input,
.base-node label.inline-label select {
  margin-left: 0 !important;
  flex: 1;
  min-width: 60px;
}

/* Ensure consistent spacing for description text */
.base-node > div > div:last-child {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 4px;
}

/* Fix BaseNode content alignment */
.base-node {
  justify-content: flex-start;
  align-items: stretch;
  box-sizing: border-box;
}

.base-node > div:first-child {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  box-sizing: border-box;
}

/* Node title styling */
.base-node h3 {
  margin: 0 0 8px 0;
  font-size: 13px;
  font-weight: 700;
  color: #ffffff;
  text-align: center;
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
}

/* Ensure consistent content wrapper */
.base-node .node-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
}

.base-node select {
  min-width: 100px;
  max-width: 100%;
  padding: 6px 8px;
  border-radius: 6px;
  background-color: rgba(26, 11, 46, 0.8);
  color: #ffffff;
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
  font-size: 11px;
  font-weight: 500;
  border: 1px solid rgba(138, 43, 226, 0.5);
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  height: 32px;
  box-sizing: border-box;
}

.base-node select:focus {
  border-color: rgba(138, 43, 226, 0.8);
  box-shadow: 0 0 0 2px rgba(138, 43, 226, 0.2);
  background-color: rgba(26, 11, 46, 0.9);
}

.base-node select:hover {
  border-color: rgba(138, 43, 226, 0.7);
  background-color: rgba(26, 11, 46, 0.9);
}

.base-node select option {
  background-color: rgba(26, 11, 46, 0.95);
  color: #ffffff;
  padding: 8px;
}

/* Enhanced ReactFlow Background Dots */
.react-flow__background {
  background-color: transparent !important;
}

.react-flow__background svg {
  opacity: 1 !important;
}

.react-flow__background circle {
  fill: #8b5cf6 !important;
  opacity: 0.6 !important;
}
