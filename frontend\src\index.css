/* Import VectorShift Fonts - with fallback */
@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=JetBrains+Mono:ital,wght@0,100..800;1,100..800&display=swap');

/* Studio Feixen Sans - Licensed Font Implementation */
@font-face {
  font-family: 'Studio Feixen Sans';
  src: url('./fonts/Studio Feixen Fonts TRIAL/Studio Feixen Family TRIAL/1 Studio Feixen Sans Family TRIAL/2 TTF/StudioFeixenSansTRIAL-Ultralight.ttf') format('truetype');
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Studio Feixen Sans';
  src: url('./fonts/Studio Feixen Fonts TRIAL/Studio Feixen Family TRIAL/1 Studio Feixen Sans Family TRIAL/2 TTF/StudioFeixenSansTRIAL-Light.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Studio Feixen Sans';
  src: url('./fonts/Studio Feixen Fonts TRIAL/Studio Feixen Family TRIAL/1 Studio Feixen Sans Family TRIAL/2 TTF/StudioFeixenSansTRIAL-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Studio Feixen Sans';
  src: url('./fonts/Studio Feixen Fonts TRIAL/Studio Feixen Family TRIAL/1 Studio Feixen Sans Family TRIAL/2 TTF/StudioFeixenSansTRIAL-Book.ttf') format('truetype');
  font-weight: 450;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Studio Feixen Sans';
  src: url('./fonts/Studio Feixen Fonts TRIAL/Studio Feixen Family TRIAL/1 Studio Feixen Sans Family TRIAL/2 TTF/StudioFeixenSansTRIAL-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Studio Feixen Sans';
  src: url('./fonts/Studio Feixen Fonts TRIAL/Studio Feixen Family TRIAL/1 Studio Feixen Sans Family TRIAL/2 TTF/StudioFeixenSansTRIAL-Semibold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Studio Feixen Sans';
  src: url('./fonts/Studio Feixen Fonts TRIAL/Studio Feixen Family TRIAL/1 Studio Feixen Sans Family TRIAL/2 TTF/StudioFeixenSansTRIAL-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

/* Studio Feixen Sans Italic Variants */
@font-face {
  font-family: 'Studio Feixen Sans';
  src: url('./fonts/Studio Feixen Fonts TRIAL/Studio Feixen Family TRIAL/1 Studio Feixen Sans Family TRIAL/2 TTF/StudioFeixenSansTRIAL-RegularItalic.ttf') format('truetype');
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Studio Feixen Sans';
  src: url('./fonts/Studio Feixen Fonts TRIAL/Studio Feixen Family TRIAL/1 Studio Feixen Sans Family TRIAL/2 TTF/StudioFeixenSansTRIAL-MediumItalic.ttf') format('truetype');
  font-weight: 500;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Studio Feixen Sans';
  src: url('./fonts/Studio Feixen Fonts TRIAL/Studio Feixen Family TRIAL/1 Studio Feixen Sans Family TRIAL/2 TTF/StudioFeixenSansTRIAL-BoldItalic.ttf') format('truetype');
  font-weight: 700;
  font-style: italic;
  font-display: swap;
}

/* CSS Reset and Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  font-family: 'Studio Feixen Sans', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

body {
  margin: 0;
  font-family: 'Studio Feixen Sans', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #1a0b2e 0%, #16213e 50%, #0f3460 100%);
  min-height: 100vh;
  color: #ffffff;
  /* Ensure font loads properly */
  font-feature-settings: "kern" 1, "liga" 1;
  text-rendering: optimizeLegibility;
}

code {
  font-family: 'JetBrains Mono', source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* App Layout - Performance Optimized */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(135deg, #1a0b2e 0%, #16213e 50%, #0f3460 100%);
  color: #ffffff;
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
  position: relative;
}

/* Modern Responsive Toolbar */
.pipeline-toolbar {
  background: rgba(26, 11, 46, 0.95);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(138, 43, 226, 0.3);
  border-radius: 12px;
  margin: 8px;
  padding: 8px 12px;
  box-shadow:
    0 4px 20px rgba(138, 43, 226, 0.2),
    0 2px 8px rgba(0, 0, 0, 0.3);
  position: relative;
  overflow: hidden;
  /* Modern flex layout */
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-height: auto;
}

/* Subtle gradient overlay matching page theme */
.pipeline-toolbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(138, 43, 226, 0.1) 0%,
    rgba(26, 11, 46, 0.05) 50%,
    rgba(138, 43, 226, 0.08) 100%);
  border-radius: 16px;
  pointer-events: none;
  z-index: 1;
}

/* Modern Responsive Header */
.toolbar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  position: relative;
  z-index: 2;
  flex-wrap: nowrap;
  min-height: 32px;
}

.toolbar-brand {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
  min-width: 0;
  max-width: 100%;
}

.brand-logo {
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-svg {
  filter: drop-shadow(0 0 8px rgba(138, 43, 226, 0.5));
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 8px;
  padding: 4px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(8px);
}

.logo-svg:hover {
  transform: scale(1.1) rotate(5deg);
  filter: drop-shadow(0 0 16px rgba(138, 43, 226, 0.8));
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(12px);
}

.brand-text {
  flex: 1;
}

.toolbar-title {
  font-size: clamp(16px, 4vw, 22px);
  font-weight: 600;
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
  color: #ffffff;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 70%, #8b5cf6 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  white-space: nowrap;
  letter-spacing: -0.02em;
  text-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

/* Responsive title behavior */
@media (max-width: 768px) {
  .toolbar-title {
    font-size: clamp(14px, 5vw, 18px);
    white-space: normal;
    line-height: 1.2;
    text-align: left;
  }
}

@media (max-width: 480px) {
  .toolbar-title {
    font-size: clamp(12px, 6vw, 16px);
  }
}

/* Modern Responsive Node Grid */
.toolbar-nodes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));
  gap: 6px;
  align-items: center;
  width: 100%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Modern Responsive Breakpoints */
/* Desktop: 9 nodes in a row */
@media (min-width: 1200px) {
  .toolbar-nodes {
    grid-template-columns: repeat(9, 1fr);
    gap: 8px;
  }
}

/* Tablet Landscape: 6 nodes in a row */
@media (min-width: 768px) and (max-width: 1199px) {
  .toolbar-nodes {
    grid-template-columns: repeat(6, 1fr);
    gap: 6px;
  }
}

/* Tablet Portrait & Mobile Landscape: 3 nodes in a row */
@media (min-width: 480px) and (max-width: 767px) {
  .toolbar-nodes {
    grid-template-columns: repeat(3, 1fr);
    gap: 6px;
  }

  .toolbar-header {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
}

/* Mobile Portrait: 3 nodes in a row, compact */
@media (max-width: 479px) {
  .toolbar-nodes {
    grid-template-columns: repeat(3, 1fr);
    gap: 4px;
  }

  .toolbar-header {
    flex-direction: column;
    align-items: stretch;
    gap: 6px;
  }
}

/* Ultra-wide screen optimization */
@media (min-width: 1920px) {
  .pipeline-toolbar {
    margin: 12px 20px;
    padding: 12px 20px;
  }

  .toolbar-nodes {
    grid-template-columns: repeat(9, 1fr);
    gap: 10px;
  }

  .draggable-node {
    height: 42px;
    font-size: 11px;
  }
}

/* Modern Responsive Draggable Nodes */
.draggable-node {
  cursor: grab;
  width: 100%;
  height: clamp(32px, 6vw, 40px);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  background: linear-gradient(135deg,
    rgba(138, 43, 226, 0.2) 0%,
    rgba(79, 70, 229, 0.15) 50%,
    rgba(168, 85, 247, 0.1) 100%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #ffffff;
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
  font-weight: 500;
  font-size: clamp(8px, 1.8vw, 10px);
  text-transform: uppercase;
  letter-spacing: 0.2px;
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  user-select: none;
  backdrop-filter: blur(12px) saturate(150%);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.2),
    0 1px 2px rgba(138, 43, 226, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
}

/* Frost glass hover effect */
.draggable-node::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(138, 43, 226, 0.2) 100%);
  border-radius: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.draggable-node:hover {
  background: linear-gradient(135deg,
    rgba(138, 43, 226, 0.35) 0%,
    rgba(79, 70, 229, 0.3) 50%,
    rgba(168, 85, 247, 0.25) 100%);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px) scale(1.02);
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.3),
    0 2px 8px rgba(138, 43, 226, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(16px) saturate(180%);
}

.draggable-node:hover::before {
  opacity: 1;
}

.draggable-node:active {
  cursor: grabbing;
  transform: translateY(0) scale(0.95);
}

/* Modern Responsive Canvas Container */
.pipeline-ui-container {
  flex: 1;
  position: relative;
  background: rgba(26, 11, 46, 0.3);
  margin: 8px;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid rgba(138, 43, 226, 0.2);
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(138, 43, 226, 0.1);
  width: calc(100% - 16px);
  height: calc(100vh - 120px);
  min-height: 300px;
  display: flex;
  flex-direction: column;
}

/* Submit Button Container - VectorShift Style */
.submit-container {
  padding: 16px 24px;
  background: rgba(26, 11, 46, 0.95);
  border-top: 1px solid rgba(138, 43, 226, 0.2);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  backdrop-filter: blur(20px);
  min-height: auto;
}

.submit-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.pipeline-stats {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-number {
  font-size: 20px;
  font-weight: 700;
  background: linear-gradient(90deg, #8b5cf6, #a855f7);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.stat-label {
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: rgba(255, 255, 255, 0.6);
}

.stat-divider {
  color: rgba(138, 43, 226, 0.5);
  font-size: 16px;
}

.getting-started {
  text-align: center;
}

.start-hint {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(138, 43, 226, 0.1);
  padding: 8px 16px;
  border-radius: 20px;
  border: 1px solid rgba(138, 43, 226, 0.2);
}

.submit-button {
  background: linear-gradient(135deg, #8b5cf6 0%, #6366f1 100%);
  color: #ffffff;
  border: none;
  padding: 10px 32px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(138, 43, 226, 0.3);
}

.submit-button:hover {
  background: linear-gradient(135deg, #a855f7 0%, #7c3aed 100%);
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(138, 43, 226, 0.4);
}

.submit-button:active {
  transform: translateY(0) scale(0.98);
}

.submit-button.disabled {
  background: linear-gradient(135deg, rgba(138, 43, 226, 0.3) 0%, rgba(99, 102, 241, 0.3) 100%);
  cursor: not-allowed;
  opacity: 0.6;
}

.submit-button.disabled:hover {
  transform: none;
  box-shadow: 0 4px 15px rgba(138, 43, 226, 0.2);
}

.submit-button:focus {
  outline: 2px solid #6366f1;
  outline-offset: 2px;
}

/* Responsive ReactFlow Controls */
.react-flow__controls {
  background: rgba(15, 12, 26, 0.95) !important;
  border: 1px solid rgba(138, 43, 226, 0.6) !important;
  border-radius: 8px !important;
  backdrop-filter: blur(12px) !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3) !important;
  bottom: 20px !important;
  left: 20px !important;
  padding: 4px !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 2px !important;
}

.react-flow__controls-button {
  background: rgba(138, 43, 226, 0.15) !important;
  border: 1px solid rgba(138, 43, 226, 0.4) !important;
  color: #ffffff !important;
  transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1) !important;
  border-radius: 6px !important;
  width: 28px !important;
  height: 28px !important;
  margin: 0 !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.react-flow__controls-button:hover {
  background: rgba(138, 43, 226, 0.3) !important;
  border-color: rgba(138, 43, 226, 0.7) !important;
  color: #ffffff !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 8px rgba(138, 43, 226, 0.3) !important;
}

.react-flow__controls-button:active {
  background: rgba(138, 43, 226, 0.4) !important;
  border-color: rgba(138, 43, 226, 0.8) !important;
  transform: translateY(0px) scale(0.95) !important;
  box-shadow: 0 1px 4px rgba(138, 43, 226, 0.4) !important;
  transition: all 100ms cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.react-flow__controls-button svg {
  fill: currentColor !important;
  width: 16px !important;
  height: 16px !important;
}

.react-flow__minimap {
  background: rgba(35, 25, 50, 0.85) !important;
  border: 1px solid rgba(138, 43, 226, 0.6) !important;
  border-radius: 8px !important;
  backdrop-filter: blur(12px) !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3) !important;
  bottom: 20px !important;
  right: 20px !important;
  width: 100px !important;
  height: 70px !important;
  overflow: hidden !important;
}

.react-flow__minimap svg {
  background: transparent !important;
}

.react-flow__edge-path {
  stroke: #8b5cf6 !important;
  stroke-width: 2px !important;
  filter: drop-shadow(0 0 4px rgba(139, 92, 246, 0.5)) !important;
}

.react-flow__edge.selected .react-flow__edge-path {
  stroke: #a855f7 !important;
  stroke-width: 3px !important;
  filter: drop-shadow(0 0 8px rgba(168, 85, 247, 0.8)) !important;
}

.react-flow__connection-line {
  stroke: #8b5cf6 !important;
  stroke-width: 2px !important;
  filter: drop-shadow(0 0 4px rgba(139, 92, 246, 0.5)) !important;
}

/* Custom scrollbar - VectorShift Style */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(26, 11, 46, 0.3);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #8b5cf6 0%, #6366f1 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #a855f7 0%, #7c3aed 100%);
}

/* VectorShift Professional Animations */
@keyframes vectorShiftGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(138, 43, 226, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(138, 43, 226, 0.5);
  }
}

@keyframes vectorShiftPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

@keyframes vectorShiftSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Apply animations to key elements */
.pipeline-toolbar {
  animation: vectorShiftSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.draggable-node {
  animation: vectorShiftSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.submit-button {
  animation: vectorShiftGlow 3s ease-in-out infinite;
}

/* Professional focus states */
.draggable-node:focus,
.submit-button:focus {
  outline: 2px solid rgba(138, 43, 226, 0.6);
  outline-offset: 2px;
}

/* Enhanced backdrop effects */
.app-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(138, 43, 226, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(79, 70, 229, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(168, 85, 247, 0.05) 0%, transparent 70%);
  pointer-events: none;
  z-index: -1;
  animation: vectorShiftBackdrop 20s ease-in-out infinite;
}

@keyframes vectorShiftBackdrop {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Professional node connection animations */
.react-flow__edge-path {
  animation: vectorShiftFlow 2s ease-in-out infinite;
}

@keyframes vectorShiftFlow {
  0%, 100% {
    stroke-dasharray: 5, 5;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dashoffset: 10;
  }
}

/* Toolbar section staggered animations */
.toolbar-section:nth-child(1) { animation-delay: 0.1s; }
.toolbar-section:nth-child(2) { animation-delay: 0.2s; }
.toolbar-section:nth-child(3) { animation-delay: 0.3s; }
.toolbar-section:nth-child(4) { animation-delay: 0.4s; }

/* Node hover glow effect */
.base-node {
  position: relative;
  overflow: visible;
}

.base-node::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, rgba(138, 43, 226, 0.3), rgba(79, 70, 229, 0.3));
  border-radius: 18px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.base-node:hover::before {
  opacity: 1;
}

/* Professional drag state */
.react-flow__node.dragging {
  transform: rotate(2deg) scale(1.05);
  filter: drop-shadow(0 10px 20px rgba(138, 43, 226, 0.4));
  z-index: 1000;
}

/* Submit button enhanced states */
.submit-button {
  position: relative;
  overflow: hidden;
}

.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.submit-button:hover::before {
  left: 100%;
}

/* VectorShift-style loading indicator */
@keyframes vectorShiftSpin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Modern Responsive Design */
/* Tablet */
@media (max-width: 768px) {
  .pipeline-ui-container {
    margin: 6px;
    width: calc(100% - 12px);
    height: calc(100vh - 100px);
    min-height: 250px;
  }

  .pipeline-toolbar {
    margin: 6px;
    padding: 6px 10px;
  }

  .submit-container {
    padding: 10px 14px;
    gap: 6px;
  }

  .submit-button {
    padding: 8px 20px;
    font-size: 12px;
  }

  .pipeline-stats {
    gap: 6px;
    font-size: 11px;
  }

  .stat-number {
    font-size: 16px;
  }

  .stat-label {
    font-size: 10px;
  }

  .base-node {
    max-width: min(150px, 30vw);
    min-width: 120px;
  }

  /* Smaller ReactFlow controls on tablet */
  .react-flow__controls {
    bottom: 15px !important;
    left: 15px !important;
    padding: 3px !important;
  }

  .react-flow__controls-button {
    width: 24px !important;
    height: 24px !important;
  }

  .react-flow__controls-button svg {
    width: 14px !important;
    height: 14px !important;
  }

  .react-flow__minimap {
    bottom: 15px !important;
    right: 15px !important;
    width: 85px !important;
    height: 55px !important;
  }
}

/* Mobile */
@media (max-width: 480px) {
  .pipeline-ui-container {
    margin: 4px;
    width: calc(100% - 8px);
    height: calc(100vh - 80px);
    min-height: 200px;
  }

  .pipeline-toolbar {
    margin: 4px;
    padding: 4px 8px;
  }

  .submit-container {
    padding: 8px 10px;
    gap: 4px;
  }

  .submit-button {
    padding: 6px 16px;
    font-size: 11px;
  }

  .pipeline-stats {
    flex-direction: column;
    gap: 2px;
  }

  .stat-divider {
    display: none;
  }

  .stat-number {
    font-size: 14px;
  }

  .stat-label {
    font-size: 9px;
  }

  .base-node {
    max-width: min(130px, 35vw);
    min-width: 100px;
  }

  /* Minimal ReactFlow controls on mobile */
  .react-flow__controls {
    bottom: 10px !important;
    left: 10px !important;
    padding: 2px !important;
  }

  .react-flow__controls-button {
    width: 22px !important;
    height: 22px !important;
    font-size: 10px !important;
  }

  .react-flow__controls-button svg {
    width: 12px !important;
    height: 12px !important;
  }

  .react-flow__minimap {
    bottom: 10px !important;
    right: 10px !important;
    width: 70px !important;
    height: 45px !important;
  }
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(138, 43, 226, 0.3);
  border-top: 2px solid #8b5cf6;
  border-radius: 50%;
  animation: vectorShiftSpin 1s linear infinite;
}

/* Professional focus indicators */
.react-flow__controls-button:focus {
  outline: 2px solid rgba(138, 43, 226, 0.6) !important;
  outline-offset: 2px !important;
  background: rgba(138, 43, 226, 0.25) !important;
  border-color: rgba(138, 43, 226, 0.7) !important;
}

/* Enhanced minimap styling */
.react-flow__minimap-mask {
  fill: rgba(30, 20, 60, 0.3) !important;
}

.react-flow__minimap-node {
  fill: #c084fc !important;
  stroke: #ffffff !important;
  stroke-width: 1.5px !important;
  opacity: 1 !important;
}

.react-flow__minimap-viewport {
  stroke: #d946ef !important;
  stroke-width: 2px !important;
  fill: rgba(217, 70, 239, 0.15) !important;
}

/* Node Input Field Styling - VectorShift Professional */
.node-input {
  min-width: 100px;
  max-width: 100%;
  white-space: nowrap;
  overflow-x: auto;
  padding: 6px 8px;
  border-radius: 6px;
  background-color: rgba(26, 11, 46, 0.8);
  color: #ffffff;
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
  font-size: 11px;
  font-weight: 500;
  border: 1px solid rgba(138, 43, 226, 0.5);
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  height: 32px;
  box-sizing: border-box;
}

.node-input:focus {
  border-color: rgba(138, 43, 226, 0.6);
  box-shadow: 0 0 0 1px rgba(138, 43, 226, 0.3), 0 2px 8px rgba(138, 43, 226, 0.1);
  background-color: rgba(26, 11, 46, 0.9);
  backdrop-filter: blur(12px);
  outline: none;
}

.node-input:hover {
  border-color: rgba(138, 43, 226, 0.5);
  background-color: rgba(26, 11, 46, 0.85);
  backdrop-filter: blur(10px);
  box-shadow: 0 1px 4px rgba(138, 43, 226, 0.1);
}

.node-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* Input container with tooltip */
.input-container {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.input-tooltip {
  position: absolute;
  bottom: 100%;
  left: 0;
  background: rgba(26, 11, 46, 0.95);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  white-space: nowrap;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 200ms ease;
  border: 1px solid rgba(138, 43, 226, 0.4);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  margin-bottom: 4px;
}

.input-container:hover .input-tooltip {
  opacity: 1;
  visibility: visible;
}

/* Modal Styling - VectorShift Professional */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: modalBackdropFadeIn 0.3s ease;
}

@keyframes modalBackdropFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modal-container {
  background: #1e1e2f;
  border-radius: 12px;
  padding: 24px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  border: 1px solid rgba(138, 43, 226, 0.4);
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(138, 43, 226, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  animation: modalSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(138, 43, 226, 0.3);
}

.modal-title {
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
  font-size: 20px;
  font-weight: 600;
  color: #f5f5f5;
  display: flex;
  align-items: center;
  gap: 8px;
}

.modal-close {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 24px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 200ms ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.modal-close:hover {
  background: rgba(138, 43, 226, 0.2);
  color: #ffffff;
}

.modal-content {
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
  font-size: 16px;
  color: #f5f5f5;
  line-height: 1.6;
}

.modal-section {
  margin-bottom: 16px;
}

.modal-section:last-child {
  margin-bottom: 0;
}

.modal-stat {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(138, 43, 226, 0.1);
  border-radius: 8px;
  margin-bottom: 8px;
  border: 1px solid rgba(138, 43, 226, 0.2);
}

.modal-stat-icon {
  font-size: 18px;
}

.modal-stat-text {
  font-weight: 500;
}

.modal-status {
  padding: 12px 16px;
  border-radius: 8px;
  margin-top: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.modal-status.success {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
  color: #22c55e;
}

.modal-status.error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #ef4444;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid rgba(138, 43, 226, 0.3);
}

.modal-button {
  padding: 8px 16px;
  border-radius: 6px;
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 200ms ease;
  border: none;
}

.modal-button.primary {
  background: linear-gradient(135deg, #8b5cf6 0%, #6366f1 100%);
  color: white;
}

.modal-button.primary:hover {
  background: linear-gradient(135deg, #a855f7 0%, #7c3aed 100%);
  transform: translateY(-1px);
}

.modal-button.secondary {
  background: rgba(138, 43, 226, 0.1);
  color: #a855f7;
  border: 1px solid rgba(138, 43, 226, 0.3);
}

.modal-button.secondary:hover {
  background: rgba(138, 43, 226, 0.2);
}

/* Textarea styling to match node inputs */
textarea.node-textarea {
  min-width: 100%;
  padding: 6px 8px;
  border-radius: 6px;
  background-color: #1b1b2b;
  color: white;
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
  border: 1px solid rgba(138, 43, 226, 0.3);
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  resize: none;
}

textarea.node-textarea:focus {
  border-color: rgba(138, 43, 226, 0.8);
  box-shadow: 0 0 0 2px rgba(138, 43, 226, 0.2);
  background-color: #1e1e2f;
}

textarea.node-textarea:hover {
  border-color: rgba(138, 43, 226, 0.6);
  background-color: #1e1e2f;
}

textarea.node-textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* Node Label Styling for Better Visibility */
.base-node label {
  color: #ffffff;
  font-weight: 600;
  font-size: 11px;
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  margin-bottom: 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
  box-sizing: border-box;
  position: relative;
}

/* Standardize all node content containers */
.base-node > div {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

/* Fix inline label styling */
.base-node label.inline-label {
  flex-direction: row;
  align-items: center;
  gap: 6px;
  font-size: 11px;
}

.base-node label.inline-label input,
.base-node label.inline-label select {
  margin-left: 0 !important;
  flex: 1;
  min-width: 60px;
}

/* Ensure consistent spacing for description text */
.base-node > div > div:last-child {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 4px;
}

/* Responsive BaseNode sizing for optimal canvas usage */
.base-node {
  justify-content: flex-start;
  align-items: stretch;
  box-sizing: border-box;
  position: relative;
  overflow: visible;
  /* Smaller nodes for better canvas usage - fit 4-8 nodes in view */
  max-width: min(140px, 22vw);
  min-width: 120px;
}

.base-node > div:first-child {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  box-sizing: border-box;
}

/* Node title styling */
.base-node h3 {
  margin: 0 0 6px 0;
  font-size: 11px;
  font-weight: 700;
  color: #ffffff;
  text-align: center;
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
}

/* Ensure consistent content wrapper */
.base-node .node-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
}

.base-node select {
  min-width: 100px;
  max-width: 100%;
  width: 100%;
  padding: 6px 28px 6px 8px; /* Add right padding for dropdown arrow */
  border-radius: 6px;
  background-color: rgba(26, 11, 46, 0.8);
  color: #ffffff;
  font-family: 'Studio Feixen Sans', 'Inter', sans-serif;
  font-size: 11px;
  font-weight: 500;
  border: 1px solid rgba(138, 43, 226, 0.5);
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1), background-image 200ms ease, backdrop-filter 200ms ease;
  outline: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  height: 32px;
  box-sizing: border-box;
  position: relative;
  backdrop-filter: blur(10px);
  /* Remove default dropdown arrow */
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  /* Custom dropdown arrow */
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ffffff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 16px;
}

/* Add a subtle glow effect on select */
.base-node select::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, rgba(138, 43, 226, 0.1), rgba(99, 102, 241, 0.1));
  border-radius: 8px;
  z-index: -1;
  opacity: 0;
  transition: opacity 300ms ease;
}

.base-node select:focus {
  border-color: rgba(138, 43, 226, 0.6);
  box-shadow: 0 0 0 1px rgba(138, 43, 226, 0.3), 0 2px 8px rgba(138, 43, 226, 0.1);
  background-color: rgba(26, 11, 46, 0.9);
  backdrop-filter: blur(12px);
  z-index: 10;
  outline: none;
  /* Animate dropdown arrow on focus - rotated arrow */
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%238b5cf6' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' transform='rotate(180)'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  animation: dropdownExpand 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.base-node select:hover {
  border-color: rgba(138, 43, 226, 0.5);
  background-color: rgba(26, 11, 46, 0.85);
  backdrop-filter: blur(10px);
  box-shadow: 0 1px 4px rgba(138, 43, 226, 0.1);
  /* Subtle arrow color change on hover */
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23a855f7' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
}

.base-node select option {
  background-color: rgba(26, 11, 46, 0.95);
  color: #ffffff;
  padding: 8px;
  transition: all 200ms ease-out;
  border-radius: 4px;
  margin: 1px 0;
  backdrop-filter: blur(10px);
  border: 1px solid transparent;
  /* Smooth option appearance with glassmorphic effect */
  animation: optionSlideIn 0.15s ease-out;
  animation-fill-mode: both;
}

.base-node select option:hover {
  background-color: rgba(138, 43, 226, 0.3);
  color: #ffffff;
  border-color: rgba(138, 43, 226, 0.4);
  box-shadow: 0 1px 4px rgba(138, 43, 226, 0.2);
  backdrop-filter: blur(12px);
}

.base-node select option:checked {
  background-color: rgba(138, 43, 226, 0.5);
  color: #ffffff;
  font-weight: 600;
  border-color: rgba(138, 43, 226, 0.6);
  box-shadow: 0 0 0 1px rgba(138, 43, 226, 0.3);
  backdrop-filter: blur(14px);
}

/* Staggered animation delays for multiple options - subtle timing */
.base-node select option:nth-child(1) { animation-delay: 0ms; }
.base-node select option:nth-child(2) { animation-delay: 20ms; }
.base-node select option:nth-child(3) { animation-delay: 40ms; }
.base-node select option:nth-child(4) { animation-delay: 60ms; }
.base-node select option:nth-child(5) { animation-delay: 80ms; }
.base-node select option:nth-child(6) { animation-delay: 100ms; }

/* Smooth dropdown opening animation */
@keyframes smoothDropdownOpen {
  0% {
    opacity: 0;
    transform: translateY(-5px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Dropdown expansion animation - subtle glassmorphic effect */
@keyframes dropdownExpand {
  0% {
    transform: scale(1) translateY(0);
    box-shadow: 0 0 0 1px rgba(138, 43, 226, 0.2), 0 2px 6px rgba(138, 43, 226, 0.1);
    backdrop-filter: blur(8px);
  }
  50% {
    transform: scale(1.01) translateY(-0.5px);
    box-shadow: 0 0 0 1px rgba(138, 43, 226, 0.4), 0 3px 10px rgba(138, 43, 226, 0.15);
    backdrop-filter: blur(12px);
  }
  100% {
    transform: scale(1) translateY(0);
    box-shadow: 0 0 0 1px rgba(138, 43, 226, 0.3), 0 2px 8px rgba(138, 43, 226, 0.1);
    backdrop-filter: blur(10px);
  }
}

@keyframes dropdownGlow {
  0%, 100% {
    box-shadow: 0 0 0 2px rgba(138, 43, 226, 0.2), 0 4px 20px rgba(138, 43, 226, 0.3);
  }
  50% {
    box-shadow: 0 0 0 2px rgba(138, 43, 226, 0.3), 0 4px 22px rgba(138, 43, 226, 0.35);
  }
}

@keyframes selectPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.003);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes optionFadeIn {
  0% {
    opacity: 0;
    transform: translateY(-3px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes optionSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-4px) scale(0.98);
    backdrop-filter: blur(6px);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    backdrop-filter: blur(10px);
  }
}

/* Apply smooth dropdown animations */
.base-node select:focus {
  animation: dropdownExpand 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.base-node select:hover {
  animation: none;
}

.base-node select:active {
  animation: none;
}

/* Enhanced dropdown container animation */
.base-node label {
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.base-node label:has(select:focus) {
  transform: none;
}

.base-node label:has(select:hover) {
  transform: none;
}

/* VectorShift-Style Canvas Background */
.react-flow__background {
  background-color: rgba(20, 10, 40, 0.95) !important;
}

.react-flow__background svg {
  opacity: 0.15 !important;
}

.react-flow__background circle {
  fill: #6366f1 !important;
  opacity: 0.3 !important;
  r: 0.8 !important;
}
